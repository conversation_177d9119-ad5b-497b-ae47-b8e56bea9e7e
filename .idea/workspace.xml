<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="19d7fae1-2da9-4908-a972-e3a59da87c0b" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/backend/dist/app.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/middlewares/cachingMiddleware.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/middlewares/cachingMiddleware.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Analytics/analytics.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Analytics/analytics.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Category/category.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Category/category.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.route.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.route.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.service.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.validation.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Course/course.validation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.model.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.model.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.route.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.route.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.service.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.validation.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Lecture/lecture.validation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Payment/payment.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Payment/payment.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Payment/payout.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Payment/payout.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/StripeConnect/stripeConnect.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/StripeConnect/stripeConnect.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/StripeConnect/stripeConnect.routes.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/StripeConnect/stripeConnect.routes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/StripeConnect/stripeConnect.service.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/StripeConnect/stripeConnect.service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/Teacher/teacher.model.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/Teacher/teacher.model.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/modules/User/user.controller.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/modules/User/user.controller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/routes/health.routes.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/routes/health.routes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/scripts/optimizeDatabase.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/scripts/optimizeDatabase.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/app/services/redis/CacheInvalidationService.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/app/services/redis/CacheInvalidationService.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/dist/server.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/dist/server.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/logs/.8441b3b7f758204e38f8d36386c3200c90397743-audit.json" beforeDir="false" afterPath="$PROJECT_DIR$/backend/logs/.8441b3b7f758204e38f8d36386c3200c90397743-audit.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/logs/error-2025-06-26.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/middlewares/cachingMiddleware.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/middlewares/cachingMiddleware.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Analytics/analytics.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Analytics/analytics.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Category/category.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Category/category.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Course/course.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Course/course.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Course/course.route.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Course/course.route.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Course/course.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Course/course.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Course/course.validation.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Course/course.validation.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.model.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.model.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.route.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.route.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.validation.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Lecture/lecture.validation.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Payment/payment.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Payment/payment.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Payment/payout.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Payment/payout.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/StripeConnect/stripeConnect.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/StripeConnect/stripeConnect.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/StripeConnect/stripeConnect.routes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/StripeConnect/stripeConnect.routes.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/StripeConnect/stripeConnect.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/StripeConnect/stripeConnect.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Teacher/teacher.interface.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Teacher/teacher.interface.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/Teacher/teacher.model.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/Teacher/teacher.model.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/modules/User/user.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/modules/User/user.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/routes/health.routes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/routes/health.routes.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/scripts/optimizeDatabase.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/scripts/optimizeDatabase.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/app/services/redis/CacheInvalidationService.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/app/services/redis/CacheInvalidationService.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/server.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/server.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/test-jwt-generation.js" beforeDir="false" afterPath="$PROJECT_DIR$/backend/test-jwt-generation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/bun.lock" beforeDir="false" afterPath="$PROJECT_DIR$/client/bun.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/client/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/client/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/App.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/App.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/AppSidebar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/AppSidebar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/AdvancedVideoPlayer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/AdvancedVideoPlayer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/CloudinaryVideoPlayer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/CloudinaryVideoPlayer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/CourseLayout.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/CourseLayout.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/EnhancedUnifiedCourseManager.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/EnhancedUnifiedCourseManager.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/LectureContent.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/LectureContent.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/LectureInteraction.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/LectureInteraction.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/LectureNotes.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/LectureNotes.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/LecturePlayer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/LecturePlayer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/RichTextEditor.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/RichTextEditor.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/UnifiedContentViewer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/UnifiedContentViewer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/UnifiedCourseManager.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/UnifiedCourseManager.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Course/VideoUploader.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Course/VideoUploader.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Dashboard/EditLecture.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Dashboard/EditLecture.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Dashboard/EnhancedTeacherDashboard.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Dashboard/EnhancedTeacherDashboard.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Dashboard/FinancialSummary.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Dashboard/FinancialSummary.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/ForgotPasswordForm.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/ForgotPasswordForm.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Lecture/QuickLectureActions.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Lecture/QuickLectureActions.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Messages/__tests__/MessageCompose.test.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Messages/__tests__/MessageList.test.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Navbar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Navbar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Performance/NavigationOptimizer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Performance/NavigationOptimizer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/PopularCourses/PopularCourseSection.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/PopularCourses/PopularCourseSection.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Reviews/__tests__/Reviews.test.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Stripe/StripeStatusTracker.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Stripe/StripeStatusTracker.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Teacher/PayoutHistory.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Teacher/PayoutHistory.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Teacher/PayoutPreferences.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Teacher/PayoutPreferences.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/Teacher/StripeConnectBanner.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/Teacher/StripeConnectBanner.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/auth/AccountConnections.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/auth/AccountConnections.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/layouts/DashboardSidebar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/layouts/DashboardSidebar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/layouts/SidebarLayout.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/layouts/SidebarLayout.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/ui/sonner.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/components/ui/sonner.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/ui/toaster.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/components/ui/use-toast.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/hooks/use-toast.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/hooks/useDashboardAnalytics.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/hooks/useDashboardAnalytics.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Auth/OTPVerificationPage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Auth/OTPVerificationPage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Course/CreateCourse.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Course/CreateCourse.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/CourseDetails.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/CourseDetails.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Student/CoursePage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Student/CoursePage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Student/LecturePage.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Student/LecturePage.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/CourseCreate.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/CourseCreate.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/CourseDetail.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/CourseLectures.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/CourseLectures.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/CourseManagement.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/Courses.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/Courses.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/EditCourse.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/EditCourse.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/LectureCreate.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/LectureCreate.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/Lectures.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/Lectures.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/Settings.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/Settings.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/pages/Teacher/StripeConnectStatus.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/pages/Teacher/StripeConnectStatus.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/api/baseApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/api/baseApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/analytics/analyticsApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/features/analytics/analyticsApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/auth/authApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/features/auth/authApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/course/course.api.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/course/courseApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/features/course/courseApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/lecture/lectureApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/features/lecture/lectureApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/payment/payment.api.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/features/payment/payment.api.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/features/student/studentApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/features/student/studentApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/redux/store.ts" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/redux/store.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/routes/router.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/routes/router.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/client/src/utils/SidebarItemsGenerator.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/client/src/utils/SidebarItemsGenerator.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/client" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2z7lV1OxCZAymY93EgYuOCbQbLi" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "junie.onboarding.icon.badge.shown": "true",
    "nodejs_interpreter_path": "/home/<USER>/.nvm/versions/node/v23.11.0/bin/node",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "settings.typescriptcompiler",
    "ts.external.directory.path": "/media/gm-hridoy/study/programming-hero/green-uni-mind/client/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="19d7fae1-2da9-4908-a972-e3a59da87c0b" name="Changes" comment="" />
      <created>1751090014834</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751090014834</updated>
      <workItem from="1751090022930" duration="114000" />
      <workItem from="1751090176607" duration="288000" />
    </task>
    <servers />
  </component>
</project>